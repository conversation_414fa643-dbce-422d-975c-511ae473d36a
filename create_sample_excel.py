#!/usr/bin/env python3
"""
Create a sample Excel file for testing the form automation tool.
"""

import pandas as pd

def create_sample_excel():
    """Create a sample Excel file with test data."""
    
    # Sample data for form testing
    data = {
        'username': ['john_doe', 'jane_smith', 'bob_wilson', 'alice_brown', 'charlie_davis'],
        'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'first_name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'last_name': ['<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'phone': ['555-0101', '555-0102', '555-0103', '555-0104', '555-0105'],
        'company': ['Tech Corp', 'Design Inc', 'Data Ltd', 'Web Co', 'App Studio'],
        'message': [
            'Hello, I am interested in your services.',
            'Please contact me about your products.',
            'I would like more information.',
            'Looking forward to hearing from you.',
            'Thank you for your time.'
        ]
    }
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Save to Excel file
    filename = 'sample_form_data.xlsx'
    df.to_excel(filename, index=False)
    
    print(f"Sample Excel file created: {filename}")
    print(f"Contains {len(df)} rows of test data")
    print("\nColumns:")
    for col in df.columns:
        print(f"  - {col}")
    
    return filename

if __name__ == "__main__":
    create_sample_excel()
