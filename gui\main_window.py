import tkinter as tk
from tkinter import ttk
from gui.file_tab import FileTab
from gui.variables_tab import VariablesTab

class MainWindow:
    """Main application window with tabbed interface."""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_widgets()
        
        # Shared data between tabs
        self.excel_data = None
        self.column_names = []
        
    def setup_window(self):
        """Configure the main window."""
        self.root.title("Form Automation Tool")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # Configure style for modern look
        style = ttk.Style()
        style.theme_use('clam')
        
        # Center the window
        self.center_window()
        
    def center_window(self):
        """Center the window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """Create the main widgets."""
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="Form Automation Tool", 
            font=('Arial', 16, 'bold')
        )
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # Create notebook (tabbed interface)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create tabs
        self.file_tab = FileTab(self.notebook, self)
        self.variables_tab = VariablesTab(self.notebook, self)
        
        # Add tabs to notebook
        self.notebook.add(self.file_tab.frame, text="📁 File Selection")
        self.notebook.add(self.variables_tab.frame, text="🔧 Variables & Browser")
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def update_status(self, message):
        """Update the status bar message."""
        self.status_var.set(message)
        self.root.update_idletasks()
        
    def set_excel_data(self, data, columns):
        """Set the Excel data to be shared between tabs."""
        self.excel_data = data
        self.column_names = columns
        # Update variables tab with new columns
        self.variables_tab.update_columns(columns)
