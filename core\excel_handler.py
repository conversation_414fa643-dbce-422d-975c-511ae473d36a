import pandas as pd
import os

class ExcelHandler:
    """Handles Excel file operations."""
    
    def __init__(self):
        self.supported_formats = ['.xlsx', '.xls']
        
    def load_excel(self, file_path):
        """
        Load Excel file and return pandas DataFrame.
        
        Args:
            file_path (str): Path to the Excel file
            
        Returns:
            pandas.DataFrame: Loaded data
            
        Raises:
            Exception: If file cannot be loaded
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {file_ext}")
            
        try:
            # Try to load the Excel file
            if file_ext == '.xlsx':
                data = pd.read_excel(file_path, engine='openpyxl')
            else:  # .xls
                data = pd.read_excel(file_path, engine='xlrd')
                
            # Basic validation
            if data.empty:
                raise ValueError("The Excel file is empty")
                
            # Clean column names (remove leading/trailing spaces)
            data.columns = data.columns.str.strip()
            
            return data
            
        except Exception as e:
            raise Exception(f"Failed to load Excel file: {str(e)}")
            
    def get_column_names(self, file_path):
        """
        Get column names from Excel file without loading all data.
        
        Args:
            file_path (str): Path to the Excel file
            
        Returns:
            list: List of column names
        """
        try:
            # Load only the first row to get column names
            data = pd.read_excel(file_path, nrows=0)
            return data.columns.str.strip().tolist()
        except Exception as e:
            raise Exception(f"Failed to get column names: {str(e)}")
            
    def validate_excel_file(self, file_path):
        """
        Validate if the Excel file can be processed.
        
        Args:
            file_path (str): Path to the Excel file
            
        Returns:
            dict: Validation result with 'valid' boolean and 'message' string
        """
        try:
            if not os.path.exists(file_path):
                return {'valid': False, 'message': 'File does not exist'}
                
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return {'valid': False, 'message': f'Unsupported format: {file_ext}'}
                
            # Try to load a small sample
            data = pd.read_excel(file_path, nrows=1)
            
            if data.empty:
                return {'valid': False, 'message': 'File is empty'}
                
            if len(data.columns) == 0:
                return {'valid': False, 'message': 'No columns found'}
                
            return {'valid': True, 'message': 'File is valid'}
            
        except Exception as e:
            return {'valid': False, 'message': f'Error reading file: {str(e)}'}
            
    def get_preview_data(self, file_path, num_rows=10):
        """
        Get preview data from Excel file.
        
        Args:
            file_path (str): Path to the Excel file
            num_rows (int): Number of rows to preview
            
        Returns:
            pandas.DataFrame: Preview data
        """
        try:
            data = pd.read_excel(file_path, nrows=num_rows)
            data.columns = data.columns.str.strip()
            return data
        except Exception as e:
            raise Exception(f"Failed to get preview data: {str(e)}")
