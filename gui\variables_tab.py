import tkinter as tk
from tkinter import ttk, messagebox
from core.browser_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from core.form_filler import FormFiller

class VariablesTab:
    """Variables and browser tabs configuration tab."""

    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.browser_handler = BrowserHandler()
        self.form_filler = None
        self.column_mappings = {}

        self.create_widgets()

    def create_widgets(self):
        """Create the variables tab widgets with clean 3-section layout."""
        self.frame = ttk.Frame(self.parent, padding="15")

        # Configure main grid - 2 columns for top, 1 for bottom
        self.frame.columnconfigure(0, weight=2, minsize=300)  # Left sidebar (40%)
        self.frame.columnconfigure(1, weight=3, minsize=450)  # Right panel (60%)
        self.frame.rowconfigure(0, weight=4)  # Top sections (80%)
        self.frame.rowconfigure(1, weight=1)  # Progress section (20%)

        # ===== LEFT SIDEBAR (40% width) =====
        left_sidebar = ttk.LabelFrame(self.frame, text="🔧 Automation Controls", padding="20")
        left_sidebar.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Browser tab selection
        ttk.Label(left_sidebar, text="Browser Tab:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        self.browser_tabs_var = tk.StringVar()
        self.browser_tabs_combo = ttk.Combobox(
            left_sidebar,
            textvariable=self.browser_tabs_var,
            state="readonly",
            font=('Arial', 9)
        )
        self.browser_tabs_combo.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Refresh button
        refresh_btn = ttk.Button(
            left_sidebar,
            text="🔄 Refresh Tabs",
            command=self.refresh_browser_tabs
        )
        refresh_btn.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # Submit button configuration
        ttk.Label(left_sidebar, text="Submit Button ID:", font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        self.submit_btn_id_var = tk.StringVar()
        submit_entry = ttk.Entry(left_sidebar, textvariable=self.submit_btn_id_var, font=('Arial', 9))
        submit_entry.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # Control buttons
        self.start_btn = ttk.Button(
            left_sidebar,
            text="▶️ Start Automation",
            command=self.start_automation
        )
        self.start_btn.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        self.stop_btn = ttk.Button(
            left_sidebar,
            text="⏹️ Stop Automation",
            command=self.stop_automation,
            state="disabled"
        )
        self.stop_btn.grid(row=6, column=0, sticky=(tk.W, tk.E))

        # Configure left sidebar grid
        left_sidebar.columnconfigure(0, weight=1)
        left_sidebar.rowconfigure(7, weight=1)  # Push everything to top

        # ===== RIGHT PANEL (60% width) =====
        right_panel = ttk.LabelFrame(self.frame, text="📋 Variable Mapping", padding="20")
        right_panel.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Instructions
        instructions = ttk.Label(
            right_panel,
            text="Map Excel columns to form element IDs:",
            font=('Arial', 11, 'bold'),
            foreground="#2E86AB"
        )
        instructions.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 15))

        # Headers
        ttk.Label(right_panel, text="Excel Column", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Label(right_panel, text="", font=('Arial', 10, 'bold')).grid(row=1, column=1, sticky=tk.W)
        ttk.Label(right_panel, text="Form Element ID", font=('Arial', 10, 'bold')).grid(row=1, column=2, sticky=tk.W)

        # Separator
        separator = ttk.Separator(right_panel, orient='horizontal')
        separator.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 15))

        # Scrollable frame for mappings
        canvas = tk.Canvas(right_panel, highlightthickness=0)
        scrollbar = ttk.Scrollbar(right_panel, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=3, column=3, sticky=(tk.N, tk.S))

        # Configure right panel grid
        right_panel.columnconfigure(0, weight=1)
        right_panel.columnconfigure(2, weight=2)
        right_panel.rowconfigure(3, weight=1)

        # ===== PROGRESS SECTION (20% height, full width) =====
        progress_section = ttk.LabelFrame(self.frame, text="📊 Progress", padding="15")
        progress_section.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))

        self.progress_var = tk.StringVar()
        self.progress_var.set("Ready to start automation")
        progress_label = ttk.Label(progress_section, textvariable=self.progress_var, font=('Arial', 10))
        progress_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        self.progress_bar = ttk.Progressbar(progress_section, mode='determinate', length=400)
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Configure progress section grid
        progress_section.columnconfigure(0, weight=1)

        # Initialize browser tabs
        self.refresh_browser_tabs()

    def refresh_browser_tabs(self):
        """Refresh the list of available browser tabs."""
        try:
            # Only update status if the main window is fully initialized
            if hasattr(self.main_window, 'status_var'):
                self.main_window.update_status("Refreshing browser tabs...")

            tabs = self.browser_handler.get_browser_tabs()

            self.browser_tabs_combo['values'] = tabs
            if tabs:
                self.browser_tabs_combo.current(0)
                if hasattr(self.main_window, 'status_var'):
                    self.main_window.update_status(f"Found {len(tabs)} browser tabs")
            else:
                if hasattr(self.main_window, 'status_var'):
                    self.main_window.update_status("No browser tabs found")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh browser tabs:\n{str(e)}")
            if hasattr(self.main_window, 'status_var'):
                self.main_window.update_status("Error refreshing browser tabs")

    def update_columns(self, columns):
        """Update the variable mapping section with Excel columns."""
        # Clear existing mappings
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        self.column_mappings = {}

        if not columns:
            no_data_frame = ttk.Frame(self.scrollable_frame)
            no_data_frame.grid(row=0, column=0, columnspan=3, pady=40)

            ttk.Label(
                no_data_frame,
                text="📄 No Excel file loaded",
                font=('Arial', 12, 'bold'),
                foreground="gray"
            ).grid(row=0, column=0)

            ttk.Label(
                no_data_frame,
                text="Please go to 'File Selection' tab and load an Excel file first.",
                font=('Arial', 10),
                foreground="gray"
            ).grid(row=1, column=0, pady=(5, 0))
            return

        # Create mapping entries for each column
        for i, column in enumerate(columns):
            # Column name label with nice styling
            col_label = ttk.Label(
                self.scrollable_frame,
                text=f"{column}",
                font=('Arial', 10, 'bold'),
                foreground="#2E86AB"
            )
            col_label.grid(row=i, column=0, sticky=tk.W, padx=(10, 20), pady=8)

            # Arrow with better styling
            arrow_label = ttk.Label(
                self.scrollable_frame,
                text="➤",
                font=('Arial', 12),
                foreground="#A8DADC"
            )
            arrow_label.grid(row=i, column=1, padx=10, pady=8)

            # Element ID entry with placeholder-like styling
            element_id_var = tk.StringVar()
            element_entry = ttk.Entry(
                self.scrollable_frame,
                textvariable=element_id_var,
                font=('Arial', 9),
                width=35
            )
            element_entry.grid(row=i, column=2, sticky=(tk.W, tk.E), padx=(10, 20), pady=8)

            # Add placeholder text effect
            element_entry.insert(0, f"Enter element ID for {column}")
            element_entry.config(foreground='gray')

            def on_focus_in(event, entry=element_entry, placeholder=f"Enter element ID for {column}"):
                if entry.get() == placeholder:
                    entry.delete(0, tk.END)
                    entry.config(foreground='black')

            def on_focus_out(event, entry=element_entry, placeholder=f"Enter element ID for {column}"):
                if not entry.get():
                    entry.insert(0, placeholder)
                    entry.config(foreground='gray')

            element_entry.bind('<FocusIn>', on_focus_in)
            element_entry.bind('<FocusOut>', on_focus_out)

            # Store the mapping
            self.column_mappings[column] = element_id_var

        # Configure grid weights for the scrollable frame
        self.scrollable_frame.columnconfigure(0, weight=1)
        self.scrollable_frame.columnconfigure(2, weight=2)

    def start_automation(self):
        """Start the form automation process."""
        try:
            # Validate inputs
            if self.main_window.excel_data is None:
                messagebox.showerror("Error", "Please load an Excel file first.")
                return

            if not self.browser_tabs_var.get():
                messagebox.showerror("Error", "Please select a browser tab.")
                return

            if not self.submit_btn_id_var.get().strip():
                messagebox.showerror("Error", "Please enter the submit button element ID.")
                return

            # Get mappings
            mappings = {}
            for column, var in self.column_mappings.items():
                element_id = var.get().strip()
                # Skip placeholder text and empty values
                if element_id and not element_id.startswith("Enter element ID for"):
                    mappings[column] = element_id

            if not mappings:
                messagebox.showerror("Error", "Please map at least one column to an element ID.")
                return

            # Initialize form filler
            self.form_filler = FormFiller(
                self.main_window.excel_data,
                mappings,
                self.submit_btn_id_var.get().strip(),
                self.browser_tabs_var.get()
            )

            # Update UI
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")

            # Start automation
            self.form_filler.start_automation(self.update_progress)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start automation:\n{str(e)}")
            self.reset_ui()

    def stop_automation(self):
        """Stop the form automation process."""
        if self.form_filler:
            self.form_filler.stop_automation()
        self.reset_ui()

    def reset_ui(self):
        """Reset the UI to initial state."""
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_var.set("Ready to start")
        self.progress_bar['value'] = 0

    def update_progress(self, current, total, message):
        """Update the progress display."""
        self.progress_var.set(f"{message} ({current}/{total})")
        self.progress_bar['value'] = (current / total) * 100 if total > 0 else 0
        self.main_window.root.update_idletasks()
