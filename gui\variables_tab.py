import tkinter as tk
from tkinter import ttk, messagebox
from core.browser_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from core.form_filler import FormFiller

class VariablesTab:
    """Variables and browser tabs configuration tab."""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.browser_handler = BrowserHandler()
        self.form_filler = None
        self.column_mappings = {}
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create the variables tab widgets."""
        self.frame = ttk.Frame(self.parent, padding="20")
        
        # Browser tabs section
        browser_section = ttk.LabelFrame(self.frame, text="Browser Tab Selection", padding="15")
        browser_section.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        
        ttk.Label(browser_section, text="Select target browser tab:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        # Browser tabs dropdown
        self.browser_tabs_var = tk.StringVar()
        self.browser_tabs_combo = ttk.Combobox(
            browser_section, 
            textvariable=self.browser_tabs_var,
            state="readonly",
            width=60
        )
        self.browser_tabs_combo.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Refresh button
        refresh_btn = ttk.Button(
            browser_section, 
            text="🔄 Refresh Browser Tabs", 
            command=self.refresh_browser_tabs
        )
        refresh_btn.grid(row=1, column=1, padx=(10, 0))
        
        # Variables mapping section
        variables_section = ttk.LabelFrame(self.frame, text="Variable Mapping", padding="15")
        variables_section.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        
        # Instructions
        instructions = ttk.Label(
            variables_section,
            text="Map Excel columns to form element IDs:",
            font=('Arial', 10, 'bold')
        )
        instructions.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 15))
        
        # Scrollable frame for mappings
        canvas = tk.Canvas(variables_section, height=200)
        scrollbar = ttk.Scrollbar(variables_section, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        
        # Submit button configuration
        submit_section = ttk.LabelFrame(self.frame, text="Submit Button Configuration", padding="15")
        submit_section.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        
        ttk.Label(submit_section, text="Submit button element ID:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.submit_btn_id_var = tk.StringVar()
        submit_entry = ttk.Entry(submit_section, textvariable=self.submit_btn_id_var, width=40)
        submit_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Control buttons
        control_section = ttk.Frame(self.frame)
        control_section.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_btn = ttk.Button(
            control_section, 
            text="▶️ Start Automation", 
            command=self.start_automation,
            style="Accent.TButton"
        )
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(
            control_section, 
            text="⏹️ Stop Automation", 
            command=self.stop_automation,
            state="disabled"
        )
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        # Progress section
        progress_section = ttk.LabelFrame(self.frame, text="Progress", padding="15")
        progress_section.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.progress_var = tk.StringVar()
        self.progress_var.set("Ready to start")
        progress_label = ttk.Label(progress_section, textvariable=self.progress_var)
        progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_section, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # Configure grid weights
        self.frame.columnconfigure(0, weight=1)
        self.frame.rowconfigure(1, weight=1)
        browser_section.columnconfigure(0, weight=1)
        variables_section.columnconfigure(0, weight=1)
        variables_section.rowconfigure(1, weight=1)
        submit_section.columnconfigure(0, weight=1)
        control_section.columnconfigure(2, weight=1)
        progress_section.columnconfigure(0, weight=1)
        
        # Initialize browser tabs
        self.refresh_browser_tabs()
        
    def refresh_browser_tabs(self):
        """Refresh the list of available browser tabs."""
        try:
            self.main_window.update_status("Refreshing browser tabs...")
            tabs = self.browser_handler.get_browser_tabs()
            
            self.browser_tabs_combo['values'] = tabs
            if tabs:
                self.browser_tabs_combo.current(0)
                self.main_window.update_status(f"Found {len(tabs)} browser tabs")
            else:
                self.main_window.update_status("No browser tabs found")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh browser tabs:\n{str(e)}")
            self.main_window.update_status("Error refreshing browser tabs")
            
    def update_columns(self, columns):
        """Update the variable mapping section with Excel columns."""
        # Clear existing mappings
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
            
        self.column_mappings = {}
        
        if not columns:
            ttk.Label(
                self.scrollable_frame, 
                text="No Excel file loaded. Please select a file first.",
                foreground="gray"
            ).grid(row=0, column=0, pady=20)
            return
            
        # Create mapping entries for each column
        for i, column in enumerate(columns):
            # Column name label
            col_label = ttk.Label(
                self.scrollable_frame, 
                text=f"{column}:",
                font=('Arial', 10)
            )
            col_label.grid(row=i, column=0, sticky=tk.W, padx=(0, 10), pady=5)
            
            # Arrow
            arrow_label = ttk.Label(self.scrollable_frame, text="======>")
            arrow_label.grid(row=i, column=1, padx=10, pady=5)
            
            # Element ID entry
            element_id_var = tk.StringVar()
            element_entry = ttk.Entry(
                self.scrollable_frame, 
                textvariable=element_id_var,
                width=30
            )
            element_entry.grid(row=i, column=2, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
            
            # Store the mapping
            self.column_mappings[column] = element_id_var
            
        # Configure grid weights for the scrollable frame
        self.scrollable_frame.columnconfigure(2, weight=1)
        
    def start_automation(self):
        """Start the form automation process."""
        try:
            # Validate inputs
            if not self.main_window.excel_data is not None:
                messagebox.showerror("Error", "Please load an Excel file first.")
                return
                
            if not self.browser_tabs_var.get():
                messagebox.showerror("Error", "Please select a browser tab.")
                return
                
            if not self.submit_btn_id_var.get().strip():
                messagebox.showerror("Error", "Please enter the submit button element ID.")
                return
                
            # Get mappings
            mappings = {}
            for column, var in self.column_mappings.items():
                element_id = var.get().strip()
                if element_id:
                    mappings[column] = element_id
                    
            if not mappings:
                messagebox.showerror("Error", "Please map at least one column to an element ID.")
                return
                
            # Initialize form filler
            self.form_filler = FormFiller(
                self.main_window.excel_data,
                mappings,
                self.submit_btn_id_var.get().strip(),
                self.browser_tabs_var.get()
            )
            
            # Update UI
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            
            # Start automation
            self.form_filler.start_automation(self.update_progress)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start automation:\n{str(e)}")
            self.reset_ui()
            
    def stop_automation(self):
        """Stop the form automation process."""
        if self.form_filler:
            self.form_filler.stop_automation()
        self.reset_ui()
        
    def reset_ui(self):
        """Reset the UI to initial state."""
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress_var.set("Ready to start")
        self.progress_bar['value'] = 0
        
    def update_progress(self, current, total, message):
        """Update the progress display."""
        self.progress_var.set(f"{message} ({current}/{total})")
        self.progress_bar['value'] = (current / total) * 100 if total > 0 else 0
        self.main_window.root.update_idletasks()
