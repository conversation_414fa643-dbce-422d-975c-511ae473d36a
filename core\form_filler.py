import time
import threading
import pandas as pd
from core.browser_handler import BrowserHandler

class FormFiller:
    """Handles the automated form filling process."""
    
    def __init__(self, excel_data, column_mappings, submit_button_id, browser_tab):
        """
        Initialize the form filler.
        
        Args:
            excel_data (pandas.DataFrame): Excel data to process
            column_mappings (dict): Mapping of column names to element IDs
            submit_button_id (str): ID of the submit button
            browser_tab (str): Description of the target browser tab
        """
        self.excel_data = excel_data
        self.column_mappings = column_mappings
        self.submit_button_id = submit_button_id
        self.browser_tab = browser_tab
        self.browser_handler = BrowserHandler()
        
        self.is_running = False
        self.current_row = 0
        self.total_rows = len(excel_data)
        self.progress_callback = None
        
    def start_automation(self, progress_callback=None):
        """
        Start the automation process in a separate thread.
        
        Args:
            progress_callback (function): Callback function for progress updates
        """
        self.progress_callback = progress_callback
        self.is_running = True
        
        # Start automation in a separate thread to avoid blocking UI
        automation_thread = threading.Thread(target=self._run_automation)
        automation_thread.daemon = True
        automation_thread.start()
        
    def stop_automation(self):
        """Stop the automation process."""
        self.is_running = False
        self.browser_handler.close_browser()
        
    def _run_automation(self):
        """Main automation loop."""
        try:
            # Connect to browser
            self._update_progress(0, "Connecting to browser...")
            if not self.browser_handler.connect_to_browser(self.browser_tab):
                self._update_progress(0, "Failed to connect to browser")
                return
                
            # Process each row
            for index, row in self.excel_data.iterrows():
                if not self.is_running:
                    break
                    
                self.current_row = index + 1
                self._update_progress(self.current_row, f"Processing row {self.current_row}")
                
                # Fill form with current row data
                if self._fill_form_row(row):
                    # Submit form
                    if self._submit_form():
                        # Wait for form to be processed
                        if self._wait_for_form_submission():
                            self._update_progress(
                                self.current_row, 
                                f"Row {self.current_row} completed successfully"
                            )
                        else:
                            self._update_progress(
                                self.current_row, 
                                f"Row {self.current_row} - form submission timeout"
                            )
                    else:
                        self._update_progress(
                            self.current_row, 
                            f"Row {self.current_row} - failed to submit form"
                        )
                        break
                else:
                    self._update_progress(
                        self.current_row, 
                        f"Row {self.current_row} - failed to fill form"
                    )
                    break
                    
                # Small delay between submissions
                time.sleep(2)
                
            if self.is_running:
                self._update_progress(self.total_rows, "Automation completed successfully!")
                
        except Exception as e:
            self._update_progress(self.current_row, f"Error: {str(e)}")
            
        finally:
            self.is_running = False
            
    def _fill_form_row(self, row):
        """
        Fill form fields with data from the current row.
        
        Args:
            row (pandas.Series): Current row data
            
        Returns:
            bool: True if successful
        """
        try:
            for column_name, element_id in self.column_mappings.items():
                if column_name in row:
                    value = row[column_name]
                    
                    # Skip empty values
                    if pd.isna(value) or str(value).strip() == '':
                        continue
                        
                    # Fill the form field
                    if not self.browser_handler.fill_input_field(element_id, value):
                        print(f"Failed to fill field {element_id} with value {value}")
                        return False
                        
            return True
            
        except Exception as e:
            print(f"Error filling form row: {e}")
            return False
            
    def _submit_form(self):
        """
        Submit the form by clicking the submit button.
        
        Returns:
            bool: True if successful
        """
        try:
            return self.browser_handler.click_button(self.submit_button_id)
        except Exception as e:
            print(f"Error submitting form: {e}")
            return False
            
    def _wait_for_form_submission(self, timeout=30):
        """
        Wait for form submission to complete by checking if fields are cleared.
        
        Args:
            timeout (int): Maximum time to wait in seconds
            
        Returns:
            bool: True if form was submitted successfully
        """
        try:
            element_ids = list(self.column_mappings.values())
            
            # Wait for form to be processed
            start_time = time.time()
            while time.time() - start_time < timeout:
                if not self.is_running:
                    return False
                    
                # Check if form fields are cleared
                if self.browser_handler.check_if_form_cleared(element_ids):
                    return True
                    
                time.sleep(1)
                
            return False
            
        except Exception as e:
            print(f"Error waiting for form submission: {e}")
            return False
            
    def _update_progress(self, current, message):
        """
        Update progress through callback.
        
        Args:
            current (int): Current row number
            message (str): Progress message
        """
        if self.progress_callback:
            self.progress_callback(current, self.total_rows, message)
            
    def get_progress_info(self):
        """
        Get current progress information.
        
        Returns:
            dict: Progress information
        """
        return {
            'current_row': self.current_row,
            'total_rows': self.total_rows,
            'is_running': self.is_running,
            'progress_percentage': (self.current_row / self.total_rows * 100) if self.total_rows > 0 else 0
        }
