# Form Automation Tool

A Python application with a modern GUI for automating form filling from Excel data using browser automation.

## Features

- **Modern Tabbed Interface**: Clean and intuitive user interface
- **Excel File Support**: Load and preview Excel files (.xlsx, .xls)
- **Browser Integration**: Detect and connect to browser tabs
- **Variable Mapping**: Map Excel columns to form element IDs
- **Automated Form Filling**: Fill forms row by row from Excel data
- **Progress Tracking**: Real-time progress monitoring
- **Error Handling**: Robust error handling and user feedback

## Requirements

- Python 3.7 or higher
- Chrome browser (recommended)
- Excel files in .xlsx or .xls format

## Installation

1. **Clone or download the application files**

2. **Install required dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install ChromeDriver** (automatic via webdriver-manager)

## Usage

### 1. Start the Application
```bash
python main.py
```

### 2. Load Excel File
- Go to the "📁 File Selection" tab
- Click "📂 Browse Excel File" to select your Excel file
- Preview the data and column names

### 3. Configure Variables and Browser
- Go to the "🔧 Variables & Browser" tab
- Click "🔄 Refresh Browser Tabs" to detect open browser tabs
- Select the target browser tab containing your form
- Map Excel columns to form element IDs:
  - Each Excel column will be shown with an input field
  - Enter the HTML element ID for each corresponding form field
- Enter the submit button element ID

### 4. Start Automation
- Click "▶️ Start Automation" to begin the process
- Monitor progress in the progress section
- Use "⏹️ Stop Automation" to stop if needed

## How It Works

1. **File Loading**: The application loads your Excel file and displays column names and preview data
2. **Browser Detection**: Detects open browser tabs for form automation
3. **Variable Mapping**: Maps Excel columns to HTML form element IDs
4. **Automation Process**:
   - Fills form fields with data from the first Excel row
   - Clicks the submit button
   - Waits for form submission (checks if fields are cleared)
   - Moves to the next row and repeats
   - Continues until all rows are processed

## Tips for Best Results

### Preparing Your Excel File
- Ensure column headers are in the first row
- Remove any empty rows at the top
- Use consistent data formats
- Save as .xlsx or .xls format

### Finding Element IDs
1. Open your form in Chrome
2. Right-click on a form field → "Inspect"
3. Look for the `id` attribute in the HTML element
4. Copy the ID value (without quotes)

Example:
```html
<input type="text" id="username" name="username">
```
Use `username` as the element ID.

### Browser Setup
- Keep the target browser tab open and active
- Ensure the form is loaded and visible
- For Chrome, you may need to start with debugging enabled:
  ```bash
  chrome.exe --remote-debugging-port=9222
  ```

## Troubleshooting

### Common Issues

**"No browser tabs found"**
- Ensure Chrome is running
- Try refreshing browser tabs
- Check if Chrome is started with debugging port

**"Failed to find element"**
- Verify the element ID is correct
- Check if the element is visible on the page
- Ensure the page is fully loaded

**"Form submission timeout"**
- Increase wait time in the code if needed
- Check if the form actually submits successfully
- Verify the submit button ID is correct

**Excel file errors**
- Ensure the file is not open in Excel
- Check file permissions
- Verify the file format is supported

### Getting Element IDs

For forms where IDs are not obvious:
1. Use browser developer tools (F12)
2. Use the element selector tool
3. Look for `id`, `name`, or other unique attributes
4. If no ID exists, you may need to modify the form or use other selectors

## File Structure

```
form-automation-tool/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── gui/                   # GUI components
│   ├── __init__.py
│   ├── main_window.py     # Main window and tabs
│   ├── file_tab.py        # File selection tab
│   └── variables_tab.py   # Variables configuration tab
└── core/                  # Core functionality
    ├── __init__.py
    ├── excel_handler.py   # Excel file operations
    ├── browser_handler.py # Browser automation
    └── form_filler.py     # Form filling logic
```

## Security Notes

- This tool interacts with your browser and web forms
- Only use with trusted websites and data
- Be cautious with sensitive information
- Test with sample data first

## Support

For issues or questions:
1. Check the troubleshooting section
2. Verify all requirements are met
3. Test with a simple form and small Excel file first

## License

This project is provided as-is for educational and automation purposes.
