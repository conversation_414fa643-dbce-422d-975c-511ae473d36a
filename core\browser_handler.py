import psutil
import time
import requests
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

class BrowserHandler:
    """Handles browser detection and interaction."""

    def __init__(self):
        self.driver = None
        self.supported_browsers = ['chrome.exe', 'firefox.exe', 'msedge.exe']

    def get_browser_tabs(self):
        """
        Get list of available browser tabs using Chrome DevTools API.

        Returns:
            list: List of browser tab descriptions with titles and URLs
        """
        tabs = []

        try:
            # First, try to get Chrome tabs via DevTools API
            chrome_tabs = self._get_chrome_tabs_via_devtools()
            if chrome_tabs:
                tabs.extend(chrome_tabs)

            # If no Chrome tabs found via DevTools, try fallback method
            if not tabs:
                tabs = self._get_browser_tabs_fallback()

        except Exception as e:
            print(f"Error getting browser tabs: {e}")
            # Fallback to generic options
            tabs = [
                "Chrome - Current Active Tab",
                "Firefox - Current Active Tab",
                "Edge - Current Active Tab"
            ]

        return tabs

    def _get_chrome_tabs_via_devtools(self):
        """
        Get Chrome tabs using DevTools API.

        Returns:
            list: List of Chrome tab descriptions
        """
        tabs = []

        # Common Chrome DevTools ports
        ports = [9222, 9223, 9224, 9225]

        for port in ports:
            try:
                # Try to connect to Chrome DevTools API
                response = requests.get(f"http://localhost:{port}/json/list", timeout=2)

                if response.status_code == 200:
                    chrome_tabs = response.json()

                    for tab in chrome_tabs:
                        # Only include actual tabs (not extensions, background pages, etc.)
                        if tab.get('type') == 'page' and tab.get('url', '').startswith(('http', 'file')):
                            title = tab.get('title', 'Untitled')
                            url = tab.get('url', '')

                            # Truncate long titles and URLs for display
                            if len(title) > 50:
                                title = title[:47] + "..."
                            if len(url) > 60:
                                url = url[:57] + "..."

                            tab_description = f"🌐 {title} - {url}"
                            tabs.append({
                                'description': tab_description,
                                'id': tab.get('id'),
                                'url': url,
                                'title': title,
                                'port': port
                            })

                    # If we found tabs on this port, no need to check other ports
                    if tabs:
                        break

            except (requests.exceptions.RequestException, json.JSONDecodeError, KeyError):
                continue

        # Convert to simple list of descriptions for the dropdown
        return [tab['description'] for tab in tabs] if tabs else []

    def _get_browser_tabs_fallback(self):
        """
        Fallback method to detect browser processes.

        Returns:
            list: List of browser descriptions
        """
        tabs = []

        try:
            # Check if Chrome is running
            chrome_running = False
            for proc in psutil.process_iter(['name']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        chrome_running = True
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if chrome_running:
                tabs.append("🌐 Chrome - Active Tab (DevTools not available)")

            # Check for other browsers
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if 'firefox' in proc_name:
                        tabs.append("🦊 Firefox - Active Tab")
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if 'msedge' in proc_name or 'edge' in proc_name:
                        tabs.append("🔷 Edge - Active Tab")
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

        except Exception as e:
            print(f"Error in fallback browser detection: {e}")

        return tabs

    def connect_to_browser(self, tab_description):
        """
        Connect to the specified browser tab.

        Args:
            tab_description (str): Description of the browser tab

        Returns:
            bool: True if connection successful
        """
        try:
            # For now, we'll create a new Chrome driver instance
            # In a more advanced implementation, we could connect to existing tabs
            if 'chrome' in tab_description.lower():
                options = webdriver.ChromeOptions()
                options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")

                try:
                    # Try to connect to existing Chrome instance
                    self.driver = webdriver.Chrome(options=options)
                except:
                    # If that fails, create a new instance
                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service)

            else:
                # Default to Chrome for other browsers
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service)

            return True

        except Exception as e:
            print(f"Error connecting to browser: {e}")
            return False

    def find_element_by_selector(self, selector_value, selector_type="id", timeout=10):
        """
        Find element by different selector types with timeout.

        Args:
            selector_value (str): Selector value to find
            selector_type (str): Type of selector ('id', 'class', 'name', 'placeholder')
            timeout (int): Timeout in seconds

        Returns:
            WebElement or None: Found element or None if not found
        """
        try:
            if not self.driver:
                return None

            wait = WebDriverWait(self.driver, timeout)

            # Choose selector strategy based on type
            if selector_type == "id":
                locator = (By.ID, selector_value)
            elif selector_type == "class":
                locator = (By.CLASS_NAME, selector_value)
            elif selector_type == "name":
                locator = (By.NAME, selector_value)
            elif selector_type == "placeholder":
                locator = (By.XPATH, f"//input[@placeholder='{selector_value}']")
            else:
                print(f"Unknown selector type: {selector_type}")
                return None

            element = wait.until(EC.presence_of_element_located(locator))
            return element

        except Exception as e:
            print(f"Error finding element with {selector_type}='{selector_value}': {e}")
            return None

    def find_element_by_id(self, element_id, timeout=10):
        """
        Find element by ID with timeout (backward compatibility).

        Args:
            element_id (str): Element ID to find
            timeout (int): Timeout in seconds

        Returns:
            WebElement or None: Found element or None if not found
        """
        return self.find_element_by_selector(element_id, "id", timeout)

    def fill_input_field(self, selector_value, value, selector_type="id"):
        """
        Fill an input field with the given value using different selector types.

        Args:
            selector_value (str): Selector value of the input field
            value (str): Value to fill
            selector_type (str): Type of selector ('id', 'class', 'name', 'placeholder')

        Returns:
            bool: True if successful
        """
        try:
            element = self.find_element_by_selector(selector_value, selector_type)
            if element:
                # Clear existing content
                element.clear()
                # Fill with new value
                element.send_keys(str(value))
                return True
            return False

        except Exception as e:
            print(f"Error filling input field with {selector_type}='{selector_value}': {e}")
            return False

    def click_button(self, element_id):
        """
        Click a button by ID.

        Args:
            element_id (str): Element ID of the button

        Returns:
            bool: True if successful
        """
        try:
            element = self.find_element_by_id(element_id)
            if element:
                element.click()
                return True
            return False

        except Exception as e:
            print(f"Error clicking button {element_id}: {e}")
            return False

    def check_if_form_cleared(self, element_ids, timeout=5):
        """
        Check if form fields are cleared (indicating form submission).

        Args:
            element_ids (list): List of element IDs to check
            timeout (int): Timeout in seconds

        Returns:
            bool: True if all fields are cleared
        """
        try:
            if not self.driver:
                return False

            # Wait a bit for form to process
            time.sleep(timeout)

            for element_id in element_ids:
                element = self.find_element_by_id(element_id, timeout=2)
                if element and element.get_attribute('value'):
                    # Field still has value, form not cleared
                    return False

            return True

        except Exception as e:
            print(f"Error checking form status: {e}")
            return False

    def check_if_form_cleared_advanced(self, selector_info, timeout=5):
        """
        Check if form fields are cleared using different selector types.

        Args:
            selector_info (list): List of tuples (selector_value, selector_type)
            timeout (int): Timeout in seconds

        Returns:
            bool: True if all fields are cleared
        """
        try:
            if not self.driver:
                return False

            # Wait a bit for form to process
            time.sleep(timeout)

            for selector_value, selector_type in selector_info:
                element = self.find_element_by_selector(selector_value, selector_type, timeout=2)
                if element and element.get_attribute('value'):
                    # Field still has value, form not cleared
                    return False

            return True

        except Exception as e:
            print(f"Error checking form status: {e}")
            return False

    def close_browser(self):
        """Close the browser driver."""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
        except Exception as e:
            print(f"Error closing browser: {e}")

    def get_current_url(self):
        """Get the current URL of the browser."""
        try:
            if self.driver:
                return self.driver.current_url
            return None
        except Exception as e:
            print(f"Error getting current URL: {e}")
            return None
