import psutil
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

class BrowserHandler:
    """Handles browser detection and interaction."""

    def __init__(self):
        self.driver = None
        self.supported_browsers = ['chrome.exe', 'firefox.exe', 'msedge.exe']

    def get_browser_tabs(self):
        """
        Get list of available browser tabs/windows.

        Returns:
            list: List of browser tab descriptions
        """
        tabs = []

        try:
            # Get running browser processes
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    if proc_info['name'] and proc_info['name'].lower() in [b.lower() for b in self.supported_browsers]:
                        # For Chrome, we can get more detailed info
                        if 'chrome' in proc_info['name'].lower():
                            cmdline = proc_info.get('cmdline', [])
                            if cmdline:
                                # Look for URLs in command line
                                for arg in cmdline:
                                    if arg.startswith('http'):
                                        tabs.append(f"Chrome - {arg}")
                                        break
                                else:
                                    tabs.append(f"Chrome - PID {proc_info['pid']}")
                        else:
                            tabs.append(f"{proc_info['name']} - PID {proc_info['pid']}")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

        except Exception as e:
            print(f"Error getting browser tabs: {e}")

        # If no specific tabs found, add generic options
        if not tabs:
            tabs = [
                "Chrome - Current Active Tab",
                "Firefox - Current Active Tab",
                "Edge - Current Active Tab"
            ]

        return tabs

    def connect_to_browser(self, tab_description):
        """
        Connect to the specified browser tab.

        Args:
            tab_description (str): Description of the browser tab

        Returns:
            bool: True if connection successful
        """
        try:
            # For now, we'll create a new Chrome driver instance
            # In a more advanced implementation, we could connect to existing tabs
            if 'chrome' in tab_description.lower():
                options = webdriver.ChromeOptions()
                options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")

                try:
                    # Try to connect to existing Chrome instance
                    self.driver = webdriver.Chrome(options=options)
                except:
                    # If that fails, create a new instance
                    service = Service(ChromeDriverManager().install())
                    self.driver = webdriver.Chrome(service=service)

            else:
                # Default to Chrome for other browsers
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service)

            return True

        except Exception as e:
            print(f"Error connecting to browser: {e}")
            return False

    def find_element_by_selector(self, selector_value, selector_type="id", timeout=10):
        """
        Find element by different selector types with timeout.

        Args:
            selector_value (str): Selector value to find
            selector_type (str): Type of selector ('id', 'class', 'name', 'placeholder')
            timeout (int): Timeout in seconds

        Returns:
            WebElement or None: Found element or None if not found
        """
        try:
            if not self.driver:
                return None

            wait = WebDriverWait(self.driver, timeout)

            # Choose selector strategy based on type
            if selector_type == "id":
                locator = (By.ID, selector_value)
            elif selector_type == "class":
                locator = (By.CLASS_NAME, selector_value)
            elif selector_type == "name":
                locator = (By.NAME, selector_value)
            elif selector_type == "placeholder":
                locator = (By.XPATH, f"//input[@placeholder='{selector_value}']")
            else:
                print(f"Unknown selector type: {selector_type}")
                return None

            element = wait.until(EC.presence_of_element_located(locator))
            return element

        except Exception as e:
            print(f"Error finding element with {selector_type}='{selector_value}': {e}")
            return None

    def find_element_by_id(self, element_id, timeout=10):
        """
        Find element by ID with timeout (backward compatibility).

        Args:
            element_id (str): Element ID to find
            timeout (int): Timeout in seconds

        Returns:
            WebElement or None: Found element or None if not found
        """
        return self.find_element_by_selector(element_id, "id", timeout)

    def fill_input_field(self, selector_value, value, selector_type="id"):
        """
        Fill an input field with the given value using different selector types.

        Args:
            selector_value (str): Selector value of the input field
            value (str): Value to fill
            selector_type (str): Type of selector ('id', 'class', 'name', 'placeholder')

        Returns:
            bool: True if successful
        """
        try:
            element = self.find_element_by_selector(selector_value, selector_type)
            if element:
                # Clear existing content
                element.clear()
                # Fill with new value
                element.send_keys(str(value))
                return True
            return False

        except Exception as e:
            print(f"Error filling input field with {selector_type}='{selector_value}': {e}")
            return False

    def click_button(self, element_id):
        """
        Click a button by ID.

        Args:
            element_id (str): Element ID of the button

        Returns:
            bool: True if successful
        """
        try:
            element = self.find_element_by_id(element_id)
            if element:
                element.click()
                return True
            return False

        except Exception as e:
            print(f"Error clicking button {element_id}: {e}")
            return False

    def check_if_form_cleared(self, element_ids, timeout=5):
        """
        Check if form fields are cleared (indicating form submission).

        Args:
            element_ids (list): List of element IDs to check
            timeout (int): Timeout in seconds

        Returns:
            bool: True if all fields are cleared
        """
        try:
            if not self.driver:
                return False

            # Wait a bit for form to process
            time.sleep(timeout)

            for element_id in element_ids:
                element = self.find_element_by_id(element_id, timeout=2)
                if element and element.get_attribute('value'):
                    # Field still has value, form not cleared
                    return False

            return True

        except Exception as e:
            print(f"Error checking form status: {e}")
            return False

    def check_if_form_cleared_advanced(self, selector_info, timeout=5):
        """
        Check if form fields are cleared using different selector types.

        Args:
            selector_info (list): List of tuples (selector_value, selector_type)
            timeout (int): Timeout in seconds

        Returns:
            bool: True if all fields are cleared
        """
        try:
            if not self.driver:
                return False

            # Wait a bit for form to process
            time.sleep(timeout)

            for selector_value, selector_type in selector_info:
                element = self.find_element_by_selector(selector_value, selector_type, timeout=2)
                if element and element.get_attribute('value'):
                    # Field still has value, form not cleared
                    return False

            return True

        except Exception as e:
            print(f"Error checking form status: {e}")
            return False

    def close_browser(self):
        """Close the browser driver."""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
        except Exception as e:
            print(f"Error closing browser: {e}")

    def get_current_url(self):
        """Get the current URL of the browser."""
        try:
            if self.driver:
                return self.driver.current_url
            return None
        except Exception as e:
            print(f"Error getting current URL: {e}")
            return None
