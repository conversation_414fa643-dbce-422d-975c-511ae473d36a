import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
from core.excel_handler import ExcelHandler

class FileTab:
    """File selection and preview tab."""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.excel_handler = ExcelHandler()
        self.current_file = None
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create the file tab widgets."""
        self.frame = ttk.Frame(self.parent, padding="20")
        
        # File selection section
        file_section = ttk.LabelFrame(self.frame, text="Excel File Selection", padding="15")
        file_section.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N), pady=(0, 20))
        
        # File path display
        self.file_path_var = tk.StringVar()
        self.file_path_var.set("No file selected")
        
        ttk.Label(file_section, text="Selected File:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        file_path_label = ttk.Label(
            file_section, 
            textvariable=self.file_path_var, 
            foreground="blue",
            font=('Arial', 9)
        )
        file_path_label.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # Browse button
        browse_btn = ttk.Button(
            file_section, 
            text="📂 Browse Excel File", 
            command=self.browse_file,
            style="Accent.TButton"
        )
        browse_btn.grid(row=2, column=0, pady=(0, 10))
        
        # Column names section
        columns_section = ttk.LabelFrame(self.frame, text="Column Names", padding="15")
        columns_section.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), pady=(0, 20))
        
        # Columns listbox with scrollbar
        columns_frame = ttk.Frame(columns_section)
        columns_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.columns_listbox = tk.Listbox(
            columns_frame, 
            height=6,
            font=('Arial', 10),
            selectmode=tk.SINGLE
        )
        columns_scrollbar = ttk.Scrollbar(columns_frame, orient=tk.VERTICAL)
        self.columns_listbox.config(yscrollcommand=columns_scrollbar.set)
        columns_scrollbar.config(command=self.columns_listbox.yview)
        
        self.columns_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        columns_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # File preview section
        preview_section = ttk.LabelFrame(self.frame, text="File Preview (First 10 rows)", padding="15")
        preview_section.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        
        # Create treeview for preview
        preview_frame = ttk.Frame(preview_section)
        preview_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.preview_tree = ttk.Treeview(preview_frame, height=8)
        preview_v_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL)
        preview_h_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL)
        
        self.preview_tree.config(
            yscrollcommand=preview_v_scrollbar.set,
            xscrollcommand=preview_h_scrollbar.set
        )
        preview_v_scrollbar.config(command=self.preview_tree.yview)
        preview_h_scrollbar.config(command=self.preview_tree.xview)
        
        self.preview_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        preview_v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        preview_h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # Configure grid weights
        self.frame.columnconfigure(0, weight=1)
        self.frame.rowconfigure(2, weight=1)
        file_section.columnconfigure(0, weight=1)
        columns_section.columnconfigure(0, weight=1)
        columns_frame.columnconfigure(0, weight=1)
        columns_frame.rowconfigure(0, weight=1)
        preview_section.columnconfigure(0, weight=1)
        preview_section.rowconfigure(0, weight=1)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
    def browse_file(self):
        """Open file dialog to select Excel file."""
        file_path = filedialog.askopenfilename(
            title="Select Excel File",
            filetypes=[
                ("Excel files", "*.xlsx *.xls"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.load_file(file_path)
            
    def load_file(self, file_path):
        """Load and display Excel file data."""
        try:
            self.main_window.update_status("Loading Excel file...")
            
            # Load Excel file
            data = self.excel_handler.load_excel(file_path)
            
            if data is not None:
                self.current_file = file_path
                self.file_path_var.set(file_path)
                
                # Update columns listbox
                self.update_columns_display(data.columns.tolist())
                
                # Update preview
                self.update_preview(data)
                
                # Share data with main window
                self.main_window.set_excel_data(data, data.columns.tolist())
                
                self.main_window.update_status(f"Loaded {len(data)} rows from Excel file")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load Excel file:\n{str(e)}")
            self.main_window.update_status("Error loading file")
            
    def update_columns_display(self, columns):
        """Update the columns listbox."""
        self.columns_listbox.delete(0, tk.END)
        for i, col in enumerate(columns, 1):
            self.columns_listbox.insert(tk.END, f"{i}. {col}")
            
    def update_preview(self, data):
        """Update the preview treeview."""
        # Clear existing data
        for item in self.preview_tree.get_children():
            self.preview_tree.delete(item)
            
        # Configure columns
        columns = data.columns.tolist()
        self.preview_tree["columns"] = columns
        self.preview_tree["show"] = "headings"
        
        # Configure column headings and widths
        for col in columns:
            self.preview_tree.heading(col, text=col)
            self.preview_tree.column(col, width=120, minwidth=80)
            
        # Insert data (first 10 rows)
        preview_data = data.head(10)
        for _, row in preview_data.iterrows():
            values = [str(val) if pd.notna(val) else "" for val in row]
            self.preview_tree.insert("", tk.END, values=values)
